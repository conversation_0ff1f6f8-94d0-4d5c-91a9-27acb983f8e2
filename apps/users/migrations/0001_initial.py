# Generated by Django 4.2.23 on 2025-08-06 01:26

from django.conf import settings
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.Char<PERSON><PERSON>(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('is_deleted', models.BooleanField(db_index=True, default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('national_id_hash', models.CharField(help_text='bcrypt hashed Kenyan national ID or anonymous identifier', max_length=128, unique=True)),
                ('name', models.CharField(blank=True, max_length=255, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True, unique=True)),
                ('role', models.CharField(choices=[('citizen', 'Citizen'), ('government_official', 'Government Official'), ('anonymous', 'Anonymous')], db_index=True, default='citizen', max_length=20)),
                ('official_level', models.CharField(blank=True, choices=[('local', 'Local Official'), ('regional', 'Regional Official'), ('national', 'National Official'), ('super_admin', 'Super Administrator')], help_text='Only applicable for government_official role', max_length=20, null=True)),
                ('role_assigned_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_deleted', models.BooleanField(db_index=True, default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(db_index=True, max_length=100)),
                ('type', models.CharField(choices=[('county', 'County'), ('sub_county', 'Sub County'), ('ward', 'Ward'), ('village', 'Village')], db_index=True, max_length=20)),
                ('level', models.IntegerField(db_index=True)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_%(class)s_set', to=settings.AUTH_USER_MODEL)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='users.location')),
            ],
            options={
                'ordering': ['level', 'name'],
            },
        ),
        migrations.CreateModel(
            name='County',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_deleted', models.BooleanField(db_index=True, default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(db_index=True, max_length=100, unique=True)),
                ('code', models.CharField(db_index=True, max_length=3, unique=True)),
                ('is_active', models.BooleanField(db_index=True, default=True)),
                ('government_email', models.EmailField(blank=True, max_length=254)),
                ('government_phone', models.CharField(blank=True, max_length=20)),
                ('timezone', models.CharField(default='Africa/Nairobi', max_length=50)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_%(class)s_set', to=settings.AUTH_USER_MODEL)),
                ('location', models.OneToOneField(limit_choices_to={'type': 'county'}, on_delete=django.db.models.deletion.CASCADE, related_name='county_tenant', to='users.location')),
            ],
            options={
                'verbose_name_plural': 'Counties',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='customuser',
            name='accessible_counties',
            field=models.ManyToManyField(blank=True, help_text='Counties this official can access (for regional+ levels)', related_name='accessible_by_officials', to='users.county'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='county',
            field=models.ForeignKey(limit_choices_to={'type': 'county'}, on_delete=django.db.models.deletion.CASCADE, related_name='county_users', to='users.location'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='deleted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_%(class)s_set', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='customuser',
            name='groups',
            field=models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='home_county',
            field=models.ForeignKey(blank=True, help_text='Home county for government officials', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='home_officials', to='users.county'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='role_assigned_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_roles', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='customuser',
            name='sub_county',
            field=models.ForeignKey(blank=True, limit_choices_to={'type': 'sub_county'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sub_county_users', to='users.location'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='tenant',
            field=models.ForeignKey(help_text='County this user belongs to - determines data access scope', on_delete=django.db.models.deletion.CASCADE, related_name='users', to='users.county'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='user_permissions',
            field=models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='village',
            field=models.ForeignKey(blank=True, limit_choices_to={'type': 'village'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='village_users', to='users.location'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='ward',
            field=models.ForeignKey(blank=True, limit_choices_to={'type': 'ward'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ward_users', to='users.location'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['parent', 'type'], name='users_locat_parent__391839_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['type', 'is_deleted'], name='users_locat_type_65459b_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['level', 'is_deleted'], name='users_locat_level_bf90e1_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='location',
            unique_together={('name', 'parent', 'type')},
        ),
        migrations.AddIndex(
            model_name='county',
            index=models.Index(fields=['is_active', 'is_deleted'], name='users_count_is_acti_59aee6_idx'),
        ),
        migrations.AddIndex(
            model_name='county',
            index=models.Index(fields=['code'], name='users_count_code_5d31ff_idx'),
        ),
        migrations.AddIndex(
            model_name='customuser',
            index=models.Index(fields=['tenant', 'role'], name='users_custo_tenant__29ab49_idx'),
        ),
        migrations.AddIndex(
            model_name='customuser',
            index=models.Index(fields=['home_county', 'official_level'], name='users_custo_home_co_03e940_idx'),
        ),
        migrations.AddIndex(
            model_name='customuser',
            index=models.Index(fields=['email'], name='idx_email'),
        ),
        migrations.AddIndex(
            model_name='customuser',
            index=models.Index(fields=['is_deleted', 'role'], name='users_custo_is_dele_ae2b85_idx'),
        ),
        migrations.AddIndex(
            model_name='customuser',
            index=models.Index(fields=['role', 'official_level'], name='users_custo_role_1e8639_idx'),
        ),
        migrations.AddIndex(
            model_name='customuser',
            index=models.Index(fields=['national_id_hash'], name='users_custo_nationa_2e49e9_idx'),
        ),
    ]
