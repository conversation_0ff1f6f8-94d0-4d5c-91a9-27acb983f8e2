{% extends 'base.html' %}

{% block title %}CivicAI - Home{% endblock %}

{% block content %}
<div class="card">
    <h2>🚀 CivicAI Hackathon Implementation</h2>
    <p>Welcome to CivicAI - A modern civic feedback system for Kenya's 47 counties.</p>
    
    <div class="success">
        ✅ <strong>Phase 1 Complete:</strong> Foundation models, tenant architecture, and role-based access control are fully implemented!
    </div>
    
    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">{{ counties_count }}</div>
            <div class="stat-label">Counties Setup</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ users_count }}</div>
            <div class="stat-label">Registered Users</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">47</div>
            <div class="stat-label">Target Counties</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">3</div>
            <div class="stat-label">User Roles</div>
        </div>
    </div>
    
    <h3>🎯 System Features</h3>
    <ul>
        <li><strong>Tenant-based Architecture:</strong> Complete data isolation between counties</li>
        <li><strong>"Invisible Boundaries":</strong> Role-based access control</li>
        <li><strong>Anonymous Feedback:</strong> Privacy-first citizen engagement</li>
        <li><strong>Government Hierarchy:</strong> Local → Regional → National access levels</li>
        <li><strong>Secure Authentication:</strong> National ID hashing with bcrypt</li>
        <li><strong>Audit Trails:</strong> Soft delete with complete tracking</li>
    </ul>
    
    <h3>🔧 Quick Actions</h3>
    <a href="/admin/" class="btn">Admin Interface</a>
    <a href="/health/" class="btn">Health Check</a>
    
    <h3>📊 Next Development Phases</h3>
    <ol>
        <li><strong>Feedback System:</strong> Submission forms, file attachments, status tracking</li>
        <li><strong>Analytics Dashboard:</strong> County statistics, response metrics</li>
        <li><strong>API & Frontend:</strong> RESTful API, modern web interface</li>
        <li><strong>Mobile Integration:</strong> Native mobile apps</li>
    </ol>
    
    <div style="margin-top: 2rem; padding: 1rem; background: #e8f4fd; border-radius: 4px;">
        <strong>🏆 Hackathon Status:</strong> Foundation complete! Ready for rapid feature development.
    </div>
</div>
{% endblock %}