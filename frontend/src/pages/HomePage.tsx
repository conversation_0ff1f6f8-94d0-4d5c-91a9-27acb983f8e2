import React from 'react';
import { Link } from 'react-router-dom';
import { MessageSquare, BarChart3, Shield, Clock } from 'lucide-react';
import Button from '../components/UI/Button';
import Card from '../components/UI/Card';

const HomePage: React.FC = () => {
  const features = [
    {
      icon: <MessageSquare className="w-8 h-8 text-blue-600" />,
      title: 'Submit Feedback',
      description: 'Share your concerns, suggestions, and ideas with government officials through our secure platform.'
    },
    {
      icon: <BarChart3 className="w-8 h-8 text-green-600" />,
      title: 'Track Progress',
      description: 'Monitor the status of your submissions and see how your feedback is being addressed.'
    },
    {
      icon: <Shield className="w-8 h-8 text-purple-600" />,
      title: 'Secure & Anonymous',
      description: 'Your privacy is protected. Submit feedback anonymously or with your identity, your choice.'
    },
    {
      icon: <Clock className="w-8 h-8 text-orange-600" />,
      title: 'Real-time Updates',
      description: 'Get notified when there are updates on your submissions or related topics in your area.'
    }
  ];

  const stats = [
    { value: '15,247', label: 'Feedback Submitted' },
    { value: '12,891', label: 'Issues Resolved' },
    { value: '89%', label: 'Satisfaction Rate' },
    { value: '24h', label: 'Avg Response Time' }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
              Your Voice Matters in
              <span className="block text-blue-200">Democratic Governance</span>
            </h1>
            <p className="text-xl sm:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed">
              Connect directly with government officials, submit feedback on public services, 
              and help shape policies that affect your community.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/feedback">
                <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">
                  Submit Feedback
                </Button>
              </Link>
              <Link to="/transparency">
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white/10 backdrop-blur-sm">
                  View Transparency Portal
                </Button>
              </Link>
            </div>
          </div>
        </div>
        
        {/* Decorative Elements */}
        <div className="absolute top-1/2 left-10 w-20 h-20 bg-blue-400/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/3 right-10 w-32 h-32 bg-blue-300/20 rounded-full blur-3xl"></div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600 dark:text-gray-400 text-sm sm:text-base">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Empowering Citizens Through Technology
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Our platform bridges the gap between citizens and government, making it easier 
              than ever to participate in the democratic process.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} hover className="text-center group">
                <div className="mb-4 flex justify-center">
                  <div className="p-3 rounded-full bg-gray-100 dark:bg-gray-700 group-hover:scale-110 transition-transform duration-200">
                    {feature.icon}
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  {feature.description}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Make a Difference?
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Join thousands of citizens who are already using our platform to create 
              positive change in their communities.
            </p>
            <Link to="/feedback">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">
                Get Started Today
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;